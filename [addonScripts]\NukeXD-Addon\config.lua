Config = {}

-- Ped Configuration
Config.Ped = {
    model = 'a_m_m_business_01', -- Ped model hash or name
    coords = vec4(0.0, 0.0, 0.0, 0.0), -- x, y, z, heading - CHANGE THESE COORDINATES
    scenario = 'WORLD_HUMAN_CLIPBOARD', -- Animation/scenario for the ped
}

-- Interaction Configuration
Config.Interaction = {
    distance = 8.0, -- Distance to show interaction
    interactDst = 1.5, -- Distance to interact
    id = 'vip_menu_ped', -- Unique ID for the interaction
    name = 'VIP Menu', -- Name of the interaction
    label = 'Open VIP Menu', -- Label shown when pressing E
}

-- Job restrictions (optional)
-- Set to nil or empty table {} to allow all players
-- Example: { ['police'] = 0, ['ambulance'] = 1 }
Config.JobRestrictions = {}
