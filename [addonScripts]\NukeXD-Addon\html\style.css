* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    overflow: hidden;
    background: transparent;
}

.hidden {
    display: none !important;
}

#peacetime-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    pointer-events: none;
}

.peacetime-banner {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    border: 2px solid #fca5a5;
    border-radius: 12px;
    padding: 12px 20px;
    box-shadow: 0 8px 32px rgba(220, 38, 38, 0.4);
    backdrop-filter: blur(10px);
    animation: slideDown 0.5s ease-out;
    min-width: 280px;
    overflow: hidden;
}

.shield-icon {
    font-size: 24px;
    margin-right: 12px;
    animation: pulse 2s infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

.text-content {
    flex: 1;
    text-align: center;
}

.main-text {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    letter-spacing: 1px;
    margin-bottom: 2px;
}

.sub-text {
    color: #fecaca;
    font-size: 12px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
    letter-spacing: 0.5px;
}

.pulse-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .peacetime-banner {
        min-width: 240px;
        padding: 10px 16px;
    }
    
    .main-text {
        font-size: 14px;
    }
    
    .sub-text {
        font-size: 11px;
    }
    
    .shield-icon {
        font-size: 20px;
    }
}

/* Hide scrollbars */
::-webkit-scrollbar {
    display: none;
}

html {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
