local ESX = exports["es_extended"]:getSharedObject()
local peacetimeActive = false

RegisterCommand('peacetime', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end

    if xPlayer.getGroup() ~= 'admin' and xPlayer.getGroup() ~= 'superadmin' then
        TriggerClientEvent('esx:showNotification', source, 'You do not have permission to use this command!')
        return
    end

    peacetimeActive = not peacetimeActive

    if peacetimeActive then
        TriggerClientEvent('esx:showNotification', -1, 'PEACETIME has been ACTIVATED by an admin!')
    else
        TriggerClientEvent('esx:showNotification', -1, 'PEACETIME has been DEACTIVATED by an admin!')
    end

    TriggerClientEvent('peacetime:toggle', -1, peacetimeActive)
    
end, true)


AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
    Wait(2000)
    TriggerClientEvent('peacetime:toggle', playerId, peacetimeActive)
    
    if peacetimeActive then
        TriggerClientEvent('esx:showNotification', playerId, 'PEACETIME is currently ACTIVE!')
    end
end)

RegisterCommand('peacestatus', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    if xPlayer.getGroup() ~= 'admin' and xPlayer.getGroup() ~= 'superadmin' then
        TriggerClientEvent('esx:showNotification', source, 'You do not have permission to use this command!')
        return
    end
    
    local status = peacetimeActive and "ACTIVE" or "INACTIVE"
    TriggerClientEvent('esx:showNotification', source, ('Peacetime is currently %s'):format(status))
    
end, true)


exports('isPeacetimeActive', function()
    return peacetimeActive
end)

exports('togglePeacetime', function(state)
    if state ~= nil then
        peacetimeActive = state
    else
        peacetimeActive = not peacetimeActive
    end
    
    TriggerClientEvent('peacetime:toggle', -1, peacetimeActive)
    return peacetimeActive
end)
