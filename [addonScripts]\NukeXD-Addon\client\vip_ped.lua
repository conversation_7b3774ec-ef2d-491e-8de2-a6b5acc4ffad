local ESX = exports["es_extended"]:getSharedObject()
local spawnedPed = nil
local pedSpawned = false


local function CreateVipPed()
    if pedSpawned then return end

    local model = GetHashKey(Config.VIP_Ped.model)

    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end

    spawnedPed = CreatePed(4, model, Config.VIP_Ped.coords.x, Config.VIP_Ped.coords.y, Config.VIP_Ped.coords.z - 1.0, Config.VIP_Ped.coords.w, false, true)

    SetEntityHeading(spawnedPed, Config.VIP_Ped.coords.w)
    FreezeEntityPosition(spawnedPed, true)
    SetEntityInvincible(spawnedPed, true)
    SetBlockingOfNonTemporaryEvents(spawnedPed, true)

    if Config.VIP_Ped.scenario then
        TaskStartScenarioInPlace(spawnedPed, Config.VIP_Ped.scenario, 0, true)
    end

    SetModelAsNoLongerNeeded(model)
    pedSpawned = true
end

local function DeleteVipPed()
    if not pedSpawned then return end

    if spawnedPed and DoesEntityExist(spawnedPed) then
        DeleteEntity(spawnedPed)
        spawnedPed = nil
    end

    pedSpawned = false
end

local function SetupInteraction()
    local interactionData = {
        coords = vec3(Config.VIP_Ped.coords.x, Config.VIP_Ped.coords.y, Config.VIP_Ped.coords.z),
        distance = Config.VIP_Interaction.distance,
        interactDst = Config.VIP_Interaction.interactDst,
        id = Config.VIP_Interaction.id,
        name = Config.VIP_Interaction.name,
        options = {
            {
                label = Config.VIP_Interaction.label,
                action = function()
                    TriggerEvent('ak4y-vipSystemv2:openMenu')
                end,
            },
        }
    }

    if Config.VIP_JobRestrictions and next(Config.VIP_JobRestrictions) then
        interactionData.groups = Config.VIP_JobRestrictions
    end

    exports.interact:AddInteraction(interactionData)
end

local function CleanupPed()
    if spawnedPed and DoesEntityExist(spawnedPed) then
        DeleteEntity(spawnedPed)
        spawnedPed = nil
    end

    exports.interact:RemoveInteraction(Config.VIP_Interaction.id)
    pedSpawned = false
end

CreateThread(function()
    local pedCoords = vec3(Config.VIP_Ped.coords.x, Config.VIP_Ped.coords.y, Config.VIP_Ped.coords.z)
    local spawnDistance = Config.VIP_Interaction.distance + Config.VIP_Performance.spawnDistance
    local deleteDistance = spawnDistance + Config.VIP_Performance.deleteDistance

    while true do
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - pedCoords)

        if distance <= spawnDistance and not pedSpawned then
            CreateVipPed()
        elseif distance > deleteDistance and pedSpawned then
            DeleteVipPed()
        end

        if distance <= deleteDistance then
            Wait(Config.VIP_Performance.nearbyCheckInterval)
        else
            Wait(Config.VIP_Performance.farAwayCheckInterval)
        end
    end
end)

AddEventHandler('esx:playerLoaded', function()
    Wait(1000)
    SetupInteraction()
end)

-- Maybe i can improve later on!!

CreateThread(function()
    while not ESX.IsPlayerLoaded() do
        Wait(1000)
    end

    Wait(1000) 
    SetupInteraction()
end)

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CleanupPed()
    end
end)
