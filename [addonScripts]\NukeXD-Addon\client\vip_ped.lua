local ESX = exports["es_extended"]:getSharedObject()
local spawnedPed = nil

-- Function to create the ped
local function CreateVipPed()
    -- Load the ped model
    local model = GetHashKey(Config.Ped.model)
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end
    
    -- Create the ped
    spawnedPed = CreatePed(4, model, Config.Ped.coords.x, Config.Ped.coords.y, Config.Ped.coords.z - 1.0, Config.Ped.coords.w, false, true)
    
    -- Set ped properties
    SetEntityHeading(spawnedPed, Config.Ped.coords.w)
    FreezeEntityPosition(spawnedPed, true)
    SetEntityInvincible(spawnedPed, true)
    SetBlockingOfNonTemporaryEvents(spawnedPed, true)
    
    -- Set ped scenario/animation
    if Config.Ped.scenario then
        TaskStartScenarioInPlace(spawnedPed, Config.Ped.scenario, 0, true)
    end
    
    -- Release model
    SetModelAsNoLongerNeeded(model)
    
    print("VIP Ped spawned successfully!")
end

-- Function to setup the interaction
local function SetupInteraction()
    local interactionData = {
        coords = vec3(Config.Ped.coords.x, Config.Ped.coords.y, Config.Ped.coords.z),
        distance = Config.Interaction.distance,
        interactDst = Config.Interaction.interactDst,
        id = Config.Interaction.id,
        name = Config.Interaction.name,
        options = {
            {
                label = Config.Interaction.label,
                action = function(entity, coords, args)
                    -- Trigger the VIP system menu event
                    TriggerEvent('ak4y-vipSystemv2:openMenu')
                end,
            },
        }
    }
    
    -- Add job restrictions if configured
    if Config.JobRestrictions and next(Config.JobRestrictions) then
        interactionData.groups = Config.JobRestrictions
    end
    
    -- Add the interaction
    exports.interact:AddInteraction(interactionData)
    
    print("VIP Interaction setup successfully!")
end

-- Function to cleanup when resource stops
local function CleanupPed()
    if spawnedPed and DoesEntityExist(spawnedPed) then
        DeleteEntity(spawnedPed)
        spawnedPed = nil
    end
    
    -- Remove the interaction
    exports.interact:RemoveInteraction(Config.Interaction.id)
end

-- Initialize when player spawns
AddEventHandler('esx:playerLoaded', function(playerData)
    Wait(2000) -- Wait a bit for everything to load
    CreateVipPed()
    SetupInteraction()
end)

-- Initialize if player is already loaded
CreateThread(function()
    while not ESX.IsPlayerLoaded() do
        Wait(1000)
    end
    
    Wait(2000) -- Wait a bit for everything to load
    CreateVipPed()
    SetupInteraction()
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CleanupPed()
    end
end)
