// Peacetime UI Controller
class PeacetimeUI {
    constructor() {
        this.container = document.getElementById('peacetime-container');
        this.isVisible = false;
        this.init();
    }

    init() {
        // Listen for messages from Lua
        window.addEventListener('message', (event) => {
            console.log('[PEACETIME UI] Received message:', event.data);
            const data = event.data;

            if (!data || !data.action) {
                console.warn('[PEACETIME UI] Invalid message data:', data);
                return;
            }

            switch (data.action) {
                case 'show':
                    this.show();
                    break;
                case 'hide':
                    this.hide();
                    break;
                case 'toggle':
                    this.toggle(data.state);
                    break;
                default:
                    console.warn('[PEACETIME UI] Unknown action:', data.action);
            }
        });

        // Debug logging
        console.log('[PEACETIME UI] Initialized successfully');
        console.log('[PEACETIME UI] Container element:', this.container);

        // Test if container exists
        if (!this.container) {
            console.error('[PEACETIME UI] Container element not found!');
        }
    }

    show() {
        console.log('[PEACETIME UI] Show called, current visibility:', this.isVisible);
        if (!this.isVisible && this.container) {
            this.container.classList.remove('hidden');
            this.isVisible = true;
            console.log('[PEACETIME UI] UI shown successfully');
        } else if (!this.container) {
            console.error('[PEACETIME UI] Cannot show - container not found');
        }
    }

    hide() {
        console.log('[PEACETIME UI] Hide called, current visibility:', this.isVisible);
        if (this.isVisible && this.container) {
            this.container.classList.add('hidden');
            this.isVisible = false;
            console.log('[PEACETIME UI] UI hidden successfully');
        } else if (!this.container) {
            console.error('[PEACETIME UI] Cannot hide - container not found');
        }
    }

    toggle(state) {
        console.log('[PEACETIME UI] Toggle called with state:', state);
        if (state) {
            this.show();
        } else {
            this.hide();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.peacetimeUI = new PeacetimeUI();
});

// Prevent context menu and selection
document.addEventListener('contextmenu', (e) => e.preventDefault());
document.addEventListener('selectstart', (e) => e.preventDefault());
document.addEventListener('dragstart', (e) => e.preventDefault());
