// Peacetime UI Controller
class PeacetimeUI {
    constructor() {
        this.container = document.getElementById('peacetime-container');
        this.isVisible = false;
        this.init();
    }

    init() {
        // Listen for messages from Lua
        window.addEventListener('message', (event) => {
            const data = event.data;

            if (!data || !data.action) return;

            switch (data.action) {
                case 'show':
                    this.show();
                    break;
                case 'hide':
                    this.hide();
                    break;
                case 'toggle':
                    this.toggle(data.state);
                    break;
            }
        });
    }

    show() {
        if (!this.isVisible && this.container) {
            this.container.classList.remove('hidden');
            this.isVisible = true;
        }
    }

    hide() {
        if (this.isVisible && this.container) {
            this.container.classList.add('hidden');
            this.isVisible = false;
        }
    }

    toggle(state) {
        if (state) {
            this.show();
        } else {
            this.hide();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.peacetimeUI = new PeacetimeUI();
});

// Prevent context menu and selection
document.addEventListener('contextmenu', (e) => e.preventDefault());
document.addEventListener('selectstart', (e) => e.preventDefault());
document.addEventListener('dragstart', (e) => e.preventDefault());
