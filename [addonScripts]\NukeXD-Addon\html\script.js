// Peacetime UI Controller
class PeacetimeUI {
    constructor() {
        this.container = document.getElementById('peacetime-container');
        this.isVisible = false;
        this.init();
    }

    init() {
        // Listen for messages from Lua
        window.addEventListener('message', (event) => {
            const data = event.data;
            
            switch (data.action) {
                case 'show':
                    this.show();
                    break;
                case 'hide':
                    this.hide();
                    break;
                case 'toggle':
                    this.toggle(data.state);
                    break;
            }
        });

        // Debug logging
        console.log('Peacetime UI initialized');
    }

    show() {
        if (!this.isVisible) {
            this.container.classList.remove('hidden');
            this.isVisible = true;
            console.log('Peacetime UI shown');
        }
    }

    hide() {
        if (this.isVisible) {
            this.container.classList.add('hidden');
            this.isVisible = false;
            console.log('Peacetime UI hidden');
        }
    }

    toggle(state) {
        if (state) {
            this.show();
        } else {
            this.hide();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.peacetimeUI = new PeacetimeUI();
});

// Prevent context menu and selection
document.addEventListener('contextmenu', (e) => e.preventDefault());
document.addEventListener('selectstart', (e) => e.preventDefault());
document.addEventListener('dragstart', (e) => e.preventDefault());
