local ESX = exports["es_extended"]:getSharedObject()
local peacetimeActive = false

RegisterNetEvent('peacetime:toggle', function(state)
    peacetimeActive = state

    if peacetimeActive then
        ESX.ShowNotification('Peacetime has been activated. No damage allowed!', 'success')
    else
        ESX.ShowNotification('Peacetime has been deactivated. PvP is now allowed!', 'error')
    end
end)

CreateThread(function()
    while true do
        if peacetimeActive then
            local playerPed = PlayerPedId()
            
            SetEntityInvincible(playerPed, true)

            SetPlayerCanDoDriveBy(PlayerId(), false)

            DisableControlAction(0, 140, true) 
            DisableControlAction(0, 141, true) 
            DisableControlAction(0, 142, true) 

            DisableControlAction(0, 24, true)  
            DisableControlAction(0, 257, true)
            DisableControlAction(0, 25, true)  
            DisableControlAction(0, 263, true) 

            DisableControlAction(0, 57, true)  
            
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetEntityInvincible(vehicle, true)
                SetVehicleCanBeVisiblyDamaged(vehicle, false)
                SetVehicleCanBreak(vehicle, false)

                SetVehicleDamageModifier(vehicle, 0.0)
            end
            
            Wait(0) 
        else
            local playerPed = PlayerPedId()
            
            SetEntityInvincible(playerPed, false)
            SetPlayerCanDoDriveBy(PlayerId(), true)
            
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetEntityInvincible(vehicle, false)
                SetVehicleCanBeVisiblyDamaged(vehicle, true)
                SetVehicleCanBreak(vehicle, true)
                SetVehicleDamageModifier(vehicle, 1.0)
            end
            
            Wait(1000)
        end
    end
end)

CreateThread(function()
    while true do
        if peacetimeActive then
            local playerPed = PlayerPedId()

            SetPedCanRagdoll(playerPed, false)

            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetVehicleExplodesOnHighExplosionDamage(vehicle, false)
                SetVehicleCanEngineOperateOnFire(vehicle, true)
            end
            
            Wait(100)
        else
            local playerPed = PlayerPedId()

            SetPedCanRagdoll(playerPed, true)

            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetVehicleExplodesOnHighExplosionDamage(vehicle, true)
                SetVehicleCanEngineOperateOnFire(vehicle, false)
            end
            
            Wait(5000)
        end
    end
end)

-- Simple custom UI drawing function
local function DrawPeacetimeUI()
    if not peacetimeActive then return end

    -- Set text properties
    SetTextFont(4)
    SetTextProportional(0)
    SetTextScale(0.5, 0.5)
    SetTextColour(255, 255, 255, 255)
    SetTextDropshadow(0, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextCentre(true)

    -- Draw background rectangle
    DrawRect(0.5, 0.05, 0.25, 0.05, 220, 20, 60, 180) -- Dark red background

    -- Draw text
    SetTextEntry("STRING")
    AddTextComponentString("🛡️ PEACETIME ACTIVE")
    DrawText(0.5, 0.035)

    -- Draw subtitle
    SetTextScale(0.35, 0.35)
    SetTextEntry("STRING")
    AddTextComponentString("No PvP/VDM Allowed")
    DrawText(0.5, 0.055)
end

-- UI drawing thread
CreateThread(function()
    while true do
        if peacetimeActive then
            DrawPeacetimeUI()
            Wait(0) -- Draw every frame when active
        else
            Wait(1000) -- Check less frequently when inactive
        end
    end
end)

RegisterCommand('peacestatus', function()
    if peacetimeActive then
        ESX.ShowNotification('Peacetime is currently ACTIVE', 'info')
    else
        ESX.ShowNotification('Peacetime is currently INACTIVE', 'info')
    end
end, false)
