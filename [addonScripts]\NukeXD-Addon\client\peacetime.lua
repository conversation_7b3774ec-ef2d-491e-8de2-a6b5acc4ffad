local peacetimeActive = false

-- Initialize NUI when resource starts
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end

    -- Initialize NUI
    SetNuiFocus(false, false)
    SetNuiFocusKeepInput(false)

    -- Send initial hide message to ensure UI is hidden
    SendNUIMessage({
        action = 'hide'
    })

    print('[PEACETIME] NUI initialized')
end)

RegisterNetEvent('peacetime:toggle', function(state)
    peacetimeActive = state

    if peacetimeActive then
        TriggerEvent('esx:showNotification', 'Peacetime has been activated. No damage allowed!')

        -- Show NUI with debug
        print('[PEACETIME] Sending show message to NUI')
        SendNUIMessage({
            action = 'show'
        })
    else
        TriggerEvent('esx:showNotification', 'Peacetime has been deactivated. PvP is now allowed!')

        -- Hide NUI with debug
        print('[PEACETIME] Sending hide message to NUI')
        SendNUIMessage({
            action = 'hide'
        })
    end
end)

CreateThread(function()
    while true do
        if peacetimeActive then
            local playerPed = PlayerPedId()
            
            SetEntityInvincible(playerPed, true)

            SetPlayerCanDoDriveBy(PlayerId(), false)

            DisableControlAction(0, 140, true) 
            DisableControlAction(0, 141, true) 
            DisableControlAction(0, 142, true) 

            DisableControlAction(0, 24, true)  
            DisableControlAction(0, 257, true)
            DisableControlAction(0, 25, true)  
            DisableControlAction(0, 263, true) 

            DisableControlAction(0, 57, true)  
            
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetEntityInvincible(vehicle, true)
                SetVehicleCanBeVisiblyDamaged(vehicle, false)
                SetVehicleCanBreak(vehicle, false)

                SetVehicleDamageModifier(vehicle, 0.0)
            end
            
            Wait(0) 
        else
            local playerPed = PlayerPedId()
            
            SetEntityInvincible(playerPed, false)
            SetPlayerCanDoDriveBy(PlayerId(), true)
            
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetEntityInvincible(vehicle, false)
                SetVehicleCanBeVisiblyDamaged(vehicle, true)
                SetVehicleCanBreak(vehicle, true)
                SetVehicleDamageModifier(vehicle, 1.0)
            end
            
            Wait(1000)
        end
    end
end)

CreateThread(function()
    while true do
        if peacetimeActive then
            local playerPed = PlayerPedId()

            SetPedCanRagdoll(playerPed, false)

            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetVehicleExplodesOnHighExplosionDamage(vehicle, false)
                SetVehicleCanEngineOperateOnFire(vehicle, true)
            end
            
            Wait(100)
        else
            local playerPed = PlayerPedId()

            SetPedCanRagdoll(playerPed, true)

            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetVehicleExplodesOnHighExplosionDamage(vehicle, true)
                SetVehicleCanEngineOperateOnFire(vehicle, false)
            end
            
            Wait(5000)
        end
    end
end)

RegisterCommand('peacestatus', function()
    if peacetimeActive then
        TriggerEvent('esx:showNotification', 'Peacetime is currently ACTIVE')
    else
        TriggerEvent('esx:showNotification', 'Peacetime is currently INACTIVE')
    end
end, false)

-- Debug command to test NUI
RegisterCommand('testpeaceui', function()
    print('[PEACETIME] Testing NUI show')
    SendNUIMessage({
        action = 'show'
    })
end, false)

RegisterCommand('hidepeaceui', function()
    print('[PEACETIME] Testing NUI hide')
    SendNUIMessage({
        action = 'hide'
    })
end, false)

-- Ensure NUI shows for players who join during peacetime
AddEventHandler('esx:playerLoaded', function()
    Wait(3000) -- Wait for everything to load

    if peacetimeActive then
        SendNUIMessage({
            action = 'show'
        })
    end
end)

-- Also ensure NUI shows when resource starts during active peacetime
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end

    Wait(2000) -- Wait for everything to initialize

    if peacetimeActive then
        SendNUIMessage({
            action = 'show'
        })
    end
end)
