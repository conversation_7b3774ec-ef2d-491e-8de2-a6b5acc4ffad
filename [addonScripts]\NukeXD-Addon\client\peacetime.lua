local ESX = exports["es_extended"]:getSharedObject()
local peacetimeActive = false

RegisterNetEvent('peacetime:toggle', function(state)
    peacetimeActive = state

    if peacetimeActive then
        ESX.ShowNotification('Peacetime has been activated. No damage allowed!', 'success')
        exports['wasabi_bridge']:showTextUI('🛡️ PEACETIME ACTIVE | No PvP/VDM Allowed', 'error')
    else
        ESX.ShowNotification('Peacetime has been deactivated. PvP is now allowed!', 'error')
        exports['wasabi_bridge']:hideTextUI()
    end
end)

CreateThread(function()
    while true do
        if peacetimeActive then
            local playerPed = PlayerPedId()
            
            SetEntityInvincible(playerPed, true)

            SetPlayerCanDoDriveBy(PlayerId(), false)

            DisableControlAction(0, 140, true) 
            DisableControlAction(0, 141, true) 
            DisableControlAction(0, 142, true) 

            DisableControlAction(0, 24, true)  
            DisableControlAction(0, 257, true)
            DisableControlAction(0, 25, true)  
            DisableControlAction(0, 263, true) 

            DisableControlAction(0, 57, true)  
            
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetEntityInvincible(vehicle, true)
                SetVehicleCanBeVisiblyDamaged(vehicle, false)
                SetVehicleCanBreak(vehicle, false)

                SetVehicleDamageModifier(vehicle, 0.0)
            end
            
            Wait(0) 
        else
            local playerPed = PlayerPedId()
            
            SetEntityInvincible(playerPed, false)
            SetPlayerCanDoDriveBy(PlayerId(), true)
            
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetEntityInvincible(vehicle, false)
                SetVehicleCanBeVisiblyDamaged(vehicle, true)
                SetVehicleCanBreak(vehicle, true)
                SetVehicleDamageModifier(vehicle, 1.0)
            end
            
            Wait(1000)
        end
    end
end)

CreateThread(function()
    while true do
        if peacetimeActive then
            local playerPed = PlayerPedId()

            SetPedCanRagdoll(playerPed, false)

            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetVehicleExplodesOnHighExplosionDamage(vehicle, false)
                SetVehicleCanEngineOperateOnFire(vehicle, true)
            end
            
            Wait(100)
        else
            local playerPed = PlayerPedId()

            SetPedCanRagdoll(playerPed, true)

            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if vehicle ~= 0 then
                SetVehicleExplodesOnHighExplosionDamage(vehicle, true)
                SetVehicleCanEngineOperateOnFire(vehicle, false)
            end
            
            Wait(5000)
        end
    end
end)

RegisterCommand('peacestatus', function()
    if peacetimeActive then
        ESX.ShowNotification('Peacetime is currently ACTIVE', 'info')
        if not exports['wasabi_bridge']:isTextUIOpen() then
            exports['wasabi_bridge']:showTextUI('🛡️ PEACETIME ACTIVE | No PvP/VDM Allowed', 'error')
        end
    else
        ESX.ShowNotification('Peacetime is currently INACTIVE', 'info')
    end
end, false)


AddEventHandler('esx:playerLoaded', function()
    Wait(3000) 

    if peacetimeActive then
        exports['wasabi_bridge']:showTextUI('🛡️ PEACETIME ACTIVE | No PvP/VDM Allowed', 'error')
    end
end)


AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    Wait(2000)

    if peacetimeActive then
        exports['wasabi_bridge']:showTextUI('🛡️ PEACETIME ACTIVE | No PvP/VDM Allowed', 'error')
    end
end)
